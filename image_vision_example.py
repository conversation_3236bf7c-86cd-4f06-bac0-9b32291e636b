#!/usr/bin/env python3
"""
Simple example of sending an image to an LLM using Braintrust Python SDK.
The LLM will describe what it sees in the image.
"""

import base64
import os
from pathlib import Path

import braintrust
from openai import OpenA<PERSON>


def describe_image(image_path: str) -> str:
    """
    Send an image to an LLM and ask it to describe what it sees.
    
    Args:
        image_path: Path to the image file
        
    Returns:
        The LLM's description of the image
    """
    # Initialize Braintrust logger
    logger = braintrust.init_logger("image-vision-example")

    # Create OpenAI client using Braintrust AI Proxy
    client = braintrust.wrap_openai(
        OpenAI(
            api_key=os.environ.get("BRAINTRUST_API_KEY"),
            base_url="https://api.braintrust.dev/v1/proxy",
            default_headers={"x-bt-use-cache": "always"},
        )
    )
    
    # Validate image file exists
    if not Path(image_path).exists():
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    # Read and encode the image as base64
    with open(image_path, "rb") as image_file:
        image_data = base64.b64encode(image_file.read()).decode()
    
    # Determine image format from file extension
    file_extension = Path(image_path).suffix.lower()
    if file_extension in ['.jpg', '.jpeg']:
        mime_type = 'image/jpeg'
    elif file_extension == '.png':
        mime_type = 'image/png'
    elif file_extension == '.gif':
        mime_type = 'image/gif'
    elif file_extension == '.webp':
        mime_type = 'image/webp'
    else:
        mime_type = 'image/jpeg'  # Default fallback
    
    # Create data URL
    data_url = f"data:{mime_type};base64,{image_data}"
    
    # Send request to LLM
    response = client.chat.completions.create(
        model="gpt-4o",  # Vision-capable model
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text", 
                        "text": "What do you see in this image? Please provide a detailed description."
                    },
                    {
                        "type": "image_url", 
                        "image_url": {"url": data_url}
                    },
                ],
            }
        ],
        max_tokens=500,
        temperature=0.1,
    )
    
    # Extract and return the response
    description = response.choices[0].message.content
    return description


def main():
    """Main function to run the image description example."""
    print("🖼️  Image Vision Example with Braintrust")
    print("=" * 50)
    
    # Get image path from user
    image_path = input("Enter the path to your image file: ").strip()
    
    # Remove quotes if user wrapped the path in quotes
    if image_path.startswith('"') and image_path.endswith('"'):
        image_path = image_path[1:-1]
    elif image_path.startswith("'") and image_path.endswith("'"):
        image_path = image_path[1:-1]
    
    try:
        print(f"\n📸 Analyzing image: {image_path}")
        print("🤖 Sending to LLM...")
        
        # Get description from LLM
        description = describe_image(image_path)
        
        print("\n✅ LLM Response:")
        print("-" * 30)
        print(description)
        print("-" * 30)
        
        print(f"\n📊 Check your Braintrust dashboard to see the logged interaction!")
        
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ An error occurred: {e}")


if __name__ == "__main__":
    # Check for required environment variable (only Braintrust API key needed for proxy)
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("❌ Error: BRAINTRUST_API_KEY environment variable is required")
        print("Please set it with: export BRAINTRUST_API_KEY='your-api-key'")
        print("ℹ️  The Braintrust AI Proxy will handle model access - no OpenAI API key needed!")
        exit(1)

    main()

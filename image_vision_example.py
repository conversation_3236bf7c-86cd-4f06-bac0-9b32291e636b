#!/usr/bin/env python3
"""
Simple example of sending an image to an LLM using Braintrust Python SDK with AI Proxy.
The LLM will describe what it sees in the image.
Uses the Braintrust AI Proxy for caching and unified model access.
"""

import base64
import os
from pathlib import Path

import braintrust
from openai import OpenA<PERSON>


def describe_image(image_path: str, prompt: str, client) -> str:
    """
    Send an image to an LLM and ask it to describe what it sees.

    Args:
        image_path: Path to the image file
        prompt: The text prompt to send with the image
        client: The OpenAI client to use

    Returns:
        The LLM's description of the image
    """
    
    # Validate image file exists
    if not Path(image_path).exists():
        raise FileNotFoundError(f"Image file not found: {image_path}")
    
    # Read and encode the image as base64
    with open(image_path, "rb") as image_file:
        image_data = base64.b64encode(image_file.read()).decode()
    
    # Determine image format from file extension
    file_extension = Path(image_path).suffix.lower()
    if file_extension in ['.jpg', '.jpeg']:
        mime_type = 'image/jpeg'
    elif file_extension == '.png':
        mime_type = 'image/png'
    elif file_extension == '.gif':
        mime_type = 'image/gif'
    elif file_extension == '.webp':
        mime_type = 'image/webp'
    else:
        mime_type = 'image/jpeg'  # Default fallback
    
    # Create data URL
    data_url = f"data:{mime_type};base64,{image_data}"
    
    # Send request to LLM
    response = client.chat.completions.create(
        model="gpt-4o",  # Vision-capable model
        messages=[
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": prompt
                    },
                    {
                        "type": "image_url", 
                        "image_url": {"url": data_url}
                    },
                ],
            }
        ],
        max_tokens=500,
        temperature=0.1,
    )
    
    # Extract and return the response
    description = response.choices[0].message.content
    return description


@braintrust.traced
def analyze_multiple_images():
    """Analyze multiple images in the same trace."""
    # Initialize Braintrust logger
    logger = braintrust.init_logger("image-vision-example")

    # Create OpenAI client using Braintrust AI Proxy
    client = braintrust.wrap_openai(
        OpenAI(
            api_key=os.environ.get("BRAINTRUST_API_KEY"),
            base_url="https://api.braintrust.dev/v1/proxy",
            default_headers={"x-bt-use-cache": "always"},
        )
    )

    # Get first image path from user
    image_path_1 = input("Enter the path to your first image file: ").strip()

    # Remove quotes if user wrapped the path in quotes
    if image_path_1.startswith('"') and image_path_1.endswith('"'):
        image_path_1 = image_path_1[1:-1]
    elif image_path_1.startswith("'") and image_path_1.endswith("'"):
        image_path_1 = image_path_1[1:-1]

    print(f"\n� Analyzing first image: {image_path_1}")
    print("🤖 Sending to LLM...")

    # Get description from LLM for first image
    description_1 = describe_image(
        image_path_1,
        "What do you see in this image? Please provide a detailed description.",
        client
    )

    print("\n✅ First Image Response:")
    print("-" * 30)
    print(description_1)
    print("-" * 30)

    # Get second image path from user
    image_path_2 = input("\nEnter the path to your second image file: ").strip()

    # Remove quotes if user wrapped the path in quotes
    if image_path_2.startswith('"') and image_path_2.endswith('"'):
        image_path_2 = image_path_2[1:-1]
    elif image_path_2.startswith("'") and image_path_2.endswith("'"):
        image_path_2 = image_path_2[1:-1]

    print(f"\n📸 Analyzing second image: {image_path_2}")
    print("🤖 Sending to LLM...")

    # Get description from LLM for second image
    description_2 = describe_image(
        image_path_2,
        "How about this one?",
        client
    )

    print("\n✅ Second Image Response:")
    print("-" * 30)
    print(description_2)
    print("-" * 30)

    return {
        "first_image": {"path": image_path_1, "description": description_1},
        "second_image": {"path": image_path_2, "description": description_2}
    }


def main():
    """Main function to run the image description example."""
    print("🖼️  Multi-Image Vision Example with Braintrust")
    print("=" * 55)

    try:
        result = analyze_multiple_images()
        print(f"\n📊 Check your Braintrust dashboard to see both interactions in the same trace!")

    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ An error occurred: {e}")


if __name__ == "__main__":
    # Check for required environment variable (only Braintrust API key needed for proxy)
    if not os.getenv("BRAINTRUST_API_KEY"):
        print("❌ Error: BRAINTRUST_API_KEY environment variable is required")
        print("Please set it with: export BRAINTRUST_API_KEY='your-api-key'")
        print("ℹ️  The Braintrust AI Proxy will handle model access - no OpenAI API key needed!")
        exit(1)

    main()

#!/usr/bin/env python3
"""
Simple script to create a dataset with 2 rows having different tags.
"""

import braintrust

def main():
    # Initialize the dataset in the specified project
    dataset = braintrust.init_dataset(
        project="pedro-project1",
        name="sample-dataset-with-tags"
    )
    
    print("Creating dataset with 2 rows...")
    
    # Insert first row with tag1
    id1 = dataset.insert(
        input="foo",
        expected="bar",
        tags=["tag1"]
    )
    print(f"Inserted record 1 with id: {id1}")
    
    # Insert second row with tag2
    id2 = dataset.insert(
        input="foo", 
        expected="bar",
        tags=["tag2"]
    )
    print(f"Inserted record 2 with id: {id2}")
    
    # Flush to ensure records are saved
    dataset.flush()
    print("Records flushed to dataset")
    
    # Display the records to verify
    print("\nDataset records:")
    for record in dataset:
        print(f"ID: {record['id']}, Input: {record['input']}, Expected: {record['expected']}, Tags: {record['tags']}")
    
    # Show dataset summary
    summary = dataset.summarize()
    print(f"\nDataset summary: {summary}")

if __name__ == "__main__":
    main()
